<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Profile</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6>Test Profile Functions</h6>
                    </div>
                    <div class="card-body">
                        <button onclick="testLogin()" class="btn btn-primary btn-sm mb-2 w-100">Login</button>
                        <button onclick="testShowWasteCollection()" class="btn btn-success btn-sm mb-2 w-100">Show Waste Collection</button>
                        <button onclick="testApiCall()" class="btn btn-warning btn-sm mb-2 w-100">Direct API Call</button>
                        <button onclick="checkFunctions()" class="btn btn-info btn-sm mb-2 w-100">Check Functions</button>
                    </div>
                </div>
            </div>
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h6>Profile Content</h6>
                    </div>
                    <div class="card-body">
                        <div id="profileContent">
                            <p>Content will appear here...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-3">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6>Debug Output</h6>
                    </div>
                    <div class="card-body">
                        <pre id="debugOutput" class="bg-light p-3" style="max-height: 300px; overflow-y: auto;"></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script type="module">
        import apiService from './src/script/api.js';
        
        // Make available globally
        window.apiService = apiService;
        
        function log(message, data = null) {
            const output = document.getElementById('debugOutput');
            const timestamp = new Date().toLocaleTimeString();
            let logMessage = `[${timestamp}] ${message}`;
            if (data) {
                logMessage += '\n' + JSON.stringify(data, null, 2);
            }
            output.textContent += logMessage + '\n\n';
            output.scrollTop = output.scrollHeight;
        }
        
        window.testLogin = async function() {
            try {
                log('Testing login...');
                const response = await apiService.login({
                    login: 'testuser',
                    password: 'password123'
                });
                log('Login successful!', response);
            } catch (error) {
                log('Login failed!', { error: error.message });
            }
        };
        
        window.testShowWasteCollection = function() {
            try {
                log('Testing showUserFeature...');
                
                // Check if function exists
                if (typeof window.showUserFeature === 'function') {
                    log('showUserFeature function found, calling...');
                    window.showUserFeature('waste-collection');
                } else {
                    log('showUserFeature function NOT found!');
                    log('Available window functions:', Object.keys(window).filter(key => typeof window[key] === 'function'));
                }
            } catch (error) {
                log('Error in testShowWasteCollection:', { error: error.message, stack: error.stack });
            }
        };
        
        window.testApiCall = async function() {
            try {
                log('Testing direct API call...');
                const response = await apiService.getMyWasteCollections();
                log('API call successful!', response);
            } catch (error) {
                log('API call failed!', { error: error.message });
            }
        };
        
        window.checkFunctions = function() {
            log('Checking available functions...');
            log('apiService available:', typeof window.apiService);
            log('showUserFeature available:', typeof window.showUserFeature);
            log('viewWasteCollectionDetails available:', typeof window.viewWasteCollectionDetails);
            
            // Check if profile-common.js is loaded
            const scripts = Array.from(document.scripts).map(s => s.src);
            log('Loaded scripts:', scripts);
        };
        
        // Initialize
        log('Debug page loaded');
    </script>
    
    <!-- Load profile-common.js to test -->
    <script type="module" src="./src/script/profile-common.js"></script>
</body>
</html>
